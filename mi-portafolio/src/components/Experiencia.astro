<!-- SECTION: Experience -->
<section id="experiencia" class="py-12 sm:py-16">
    <div class="flex items-center gap-3 mb-6 sm:mb-8 justify-center md:justify-start">
        <div class="bg-sky-500/20 rounded-full p-2 sm:p-3 section-icon">
            <svg class="w-5 h-5 sm:w-6 sm:h-6 text-sky-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 13.255A23.931 23.931 0 0112 15c-3.183 0-6.22-.62-9-1.745M16 6V4a2 2 0 00-2-2h-4a2 2 0 00-2-2v2m8 0V6a2 2 0 012 2v6a2 2 0 01-2 2H6a2 2 0 01-2-2V8a2 2 0 012-2V6"></path>
            </svg>
        </div>
        <h2 class="text-2xl sm:text-3xl font-bold text-white">Experiencia Profesional</h2>
    </div>

    <!-- Timeline Container -->
    <div class="relative">
        <!-- Timeline Line - Hidden on mobile, visible on desktop -->
        <div class="hidden md:block absolute left-1/3 top-0 bottom-0 w-0.5 bg-gradient-to-b from-sky-400 via-purple-400 to-green-400"></div>
        <!-- Mobile Timeline Line -->
        <div class="block md:hidden absolute left-4 top-0 bottom-0 w-0.5 bg-gradient-to-b from-sky-400 via-purple-400 to-green-400"></div>

        <div class="space-y-8 sm:space-y-12">
            <!-- Job 1 -->
            <div class="relative">
                <!-- Timeline Dot -->
                <div class="absolute left-3.5 md:left-1/3 top-2 w-2 h-2 md:w-3 md:h-3 bg-sky-400 rounded-full border-2 border-slate-900 shadow-lg z-10 timeline-dot transform md:-translate-x-1.5"></div>

                <div class="flex flex-col md:flex-row gap-4 sm:gap-6">
                    <div class="pl-8 md:pl-0 md:w-1/3 md:pr-6">
                        <p class="text-sky-400 font-semibold text-sm sm:text-base md:text-right">2021 - Presente</p>
                    </div>
                    <div class="w-full md:w-2/3">
                        <h3 class="text-lg sm:text-xl font-bold text-white">Desarrollador de Software Full-Stack | Mundos Virtuales SPA</h3>
                <p class="mt-2 text-slate-400 text-sm sm:text-base leading-relaxed">
                    Ingeniero full-stack especializado en el desarrollo completo de soluciones software-hardware. Lideré la modernización de un sistema transaccional desde una aplicación de escritorio (C# / Windows Forms) hacia una solución móvil nativa para terminales POS (PAX A8700).
                </p>
                <div class="mt-4">
                    <h4 class="text-base sm:text-lg font-semibold text-white mb-2">Logros principales:</h4>
                    <div class="experience-content">
                        <ul class="text-slate-400 space-y-2 list-disc list-inside experience-preview text-sm sm:text-base leading-relaxed">
                            <li>Desarrollé desde cero sistema completo de conteo de billetes con interfaz Windows Forms (C#) incluyendo autenticación, dashboard, estadísticas y pantalla de conteo principal</li>
                            <li>Integré hardware especializado implementando comunicación serial con validadora de billetes para procesamiento automático de transacciones</li>
                            <li>Diseñé y desarrollé placa de control personalizada con Arduino y KiCad para centralizar control de actuadores y monitoreo de sensores en tiempo real</li>
                        </ul>
                        <ul class="text-slate-400 space-y-2 list-disc list-inside experience-full hidden text-sm sm:text-base leading-relaxed">
                            <li>Desarrollé desde cero sistema completo de conteo de billetes con interfaz Windows Forms (C#) incluyendo autenticación, dashboard, estadísticas y pantalla de conteo principal</li>
                            <li>Integré hardware especializado implementando comunicación serial con validadora de billetes para procesamiento automático de transacciones</li>
                            <li>Diseñé y desarrollé placa de control personalizada con Arduino y KiCad para centralizar control de actuadores y monitoreo de sensores en tiempo real</li>
                            <li>Implementé sistema de control automatizado donde sensores activos disparan apertura de shutter via actuador, permitiendo flujo controlado de billetes</li>
                            <li>Arquitecté backend escalable con ASP.NET Core 8 y API RESTful para persistencia y gestión de datos transaccionales</li>
                            <li>Migré solución completa a Android nativo manteniendo toda la funcionalidad hardware-software en plataforma móvil</li>
                            <li>Automaticé soporte técnico regional con plataforma n8n integrando WhatsApp/Telegram para que técnicos consulten IA especializada con RAG sobre manuales Glory UWF</li>
                            <li>Modernicé aplicación legacy refactorizando código Python con PyQt6 para integración con contadora Glory UWF via red</li>
                        </ul>
                        <button class="toggle-experience mt-3 text-sky-400 hover:text-sky-300 text-sm font-medium transition-colors duration-200">
                            Ver más
                        </button>
                    </div>
                </div>
                        <div class="mt-4">
                            <p class="text-xs sm:text-sm text-sky-300"><strong>Stack técnico:</strong> C#, Kotlin, Python, PyQt6, Android, ASP.NET 8, PostgreSQL, Arduino, KiCad, Jetpack Compose, n8n, RAG, IA/LLM</p>
                        </div>
                    </div>
                </div>
            </div>
            <!-- Job 2 -->
            <div class="relative">
                <!-- Timeline Dot -->
                <div class="absolute left-3.5 md:left-1/3 top-2 w-2 h-2 md:w-3 md:h-3 bg-purple-400 rounded-full border-2 border-slate-900 shadow-lg z-10 timeline-dot transform md:-translate-x-1.5"></div>

                <div class="flex flex-col md:flex-row gap-4 sm:gap-6">
                    <div class="pl-8 md:pl-0 md:w-1/3 md:pr-6">
                        <p class="text-sky-400 font-semibold text-sm sm:text-base md:text-right">2018 - 2021</p>
                    </div>
                    <div class="w-full md:w-2/3">
                        <h3 class="text-lg sm:text-xl font-bold text-white">Embedded Systems Engineer en Permaquim SPA</h3>
                        <p class="mt-2 text-slate-400 text-sm sm:text-base leading-relaxed">
                            Optimicé y refactoricé el firmware de bajo nivel escrito en Lenguaje C para microcontroladores PIC (PIC18F4520), utilizando el entorno de desarrollo MPLAB.
                        </p>
                        <p class="mt-2 text-slate-400 text-sm sm:text-base leading-relaxed">
                            El software gestiona el control y lectura de los sensores en una máquina depositaria (modelo P600), incluyendo la apertura de la bóveda y el mecanismo shutter para el paso de billetes.
                        </p>
                        <p class="mt-2 text-slate-400 text-sm sm:text-base leading-relaxed">
                            Mi trabajo se enfocó en mejorar la fiabilidad, el rendimiento y la mantenibilidad del código existente, asegurando una comunicación robusta con el PC y una lectura precisa de los estados de los sensores.
                        </p>
                    </div>
                </div>
            </div>

            <!-- Job 3 -->
            <div class="relative">
                <!-- Timeline Dot -->
                <div class="absolute left-3.5 md:left-1/3 top-2 w-2 h-2 md:w-3 md:h-3 bg-green-400 rounded-full border-2 border-slate-900 shadow-lg z-10 timeline-dot transform md:-translate-x-1.5"></div>

                <div class="flex flex-col md:flex-row gap-4 sm:gap-6">
                    <div class="pl-8 md:pl-0 md:w-1/3 md:pr-6">
                        <p class="text-sky-400 font-semibold text-sm sm:text-base md:text-right">2014 - 2016</p>
                    </div>
                    <div class="w-full md:w-2/3">
                        <h3 class="text-lg sm:text-xl font-bold text-white">Service Desk Analyst | TMH International Group</h3>
                        <p class="mt-2 text-slate-400 text-sm sm:text-base leading-relaxed">
                            Administrador de sistemas y soporte técnico integral responsable de la infraestructura tecnológica empresarial. Gestioné servidores Windows Server y Linux, administré bases de datos y proporcioné soporte técnico completo a usuarios finales.
                        </p>
                        <div class="mt-4">
                            <h4 class="text-base sm:text-lg font-semibold text-white mb-2">Responsabilidades principales:</h4>
                            <div class="experience-content">
                                <ul class="text-slate-400 space-y-2 list-disc list-inside experience-preview text-sm sm:text-base leading-relaxed">
                                    <li>Administración y mantenimiento de bases de datos del sistema empresarial</li>
                                    <li>Gestión de sitios web corporativos mediante cPanel y administración de hosting</li>
                                    <li>Instalación y configuración de equipos, software corporativo y creación de cuentas de correo</li>
                                </ul>
                                <ul class="text-slate-400 space-y-2 list-disc list-inside experience-full hidden text-sm sm:text-base leading-relaxed">
                                    <li>Administración y mantenimiento de bases de datos del sistema empresarial</li>
                                    <li>Gestión de sitios web corporativos mediante cPanel y administración de hosting</li>
                                    <li>Instalación y configuración de equipos, software corporativo y creación de cuentas de correo</li>
                                    <li>Administración de central telefónica VoIP utilizando Digium y Elastix</li>
                                    <li>Operación y mantenimiento de servidores Windows Server (Active Directory, Exchange, Proxy)</li>
                                    <li>Administración de sistemas Linux (CentOS), FreeBSD y dispositivos NAS</li>
                                    <li>Soporte técnico integral: redes LAN, dispositivos de impresión, correo electrónico</li>
                                    <li>Gestión de accesos y permisos de software de oficina para usuarios corporativos</li>
                                </ul>
                                <button class="toggle-experience mt-3 text-sky-400 hover:text-sky-300 text-sm font-medium transition-colors duration-200">
                                    Ver más
                                </button>
                            </div>
                        </div>
                        <div class="mt-4">
                            <p class="text-xs sm:text-sm text-sky-300"><strong>Tecnologías:</strong> Windows Server, Linux (CentOS), FreeBSD, Active Directory, Exchange Server, VoIP (Digium/Elastix), cPanel, NAS, Redes LAN</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<script>
    document.addEventListener('DOMContentLoaded', () => {
        const toggleButtons = document.querySelectorAll('.toggle-experience') as NodeListOf<HTMLButtonElement>;

        toggleButtons.forEach(button => {
            button.addEventListener('click', () => {
                const experienceContent = button.closest('.experience-content') as HTMLElement;
                const preview = experienceContent.querySelector('.experience-preview') as HTMLElement;
                const full = experienceContent.querySelector('.experience-full') as HTMLElement;

                if (full.classList.contains('hidden')) {
                    // Show full content
                    preview.classList.add('hidden');
                    full.classList.remove('hidden');
                    button.textContent = 'Ver menos';
                } else {
                    // Show preview content
                    full.classList.add('hidden');
                    preview.classList.remove('hidden');
                    button.textContent = 'Ver más';
                }
            });
        });
    });
</script>