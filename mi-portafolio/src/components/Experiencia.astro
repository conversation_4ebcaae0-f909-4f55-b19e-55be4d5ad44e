<!-- SECTION: Experience -->
<section id="experiencia" class="py-12 sm:py-16">
    <div class="flex items-center gap-3 mb-6 sm:mb-8 justify-center md:justify-start">
        <div class="bg-sky-500/20 rounded-full p-2 sm:p-3 section-icon">
            <svg class="w-5 h-5 sm:w-6 sm:h-6 text-sky-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4"></path>
            </svg>
        </div>
        <h2 class="text-2xl sm:text-3xl font-bold text-white">Experiencia Profesional</h2>
    </div>

    <!-- Experience Cards -->
    <div class="space-y-6">
        <!-- Job 1 -->
        <div class="bg-slate-800/50 rounded-xl p-6 border-l-4 border-sky-400 hover:bg-slate-800/70 transition-all duration-300">
            <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between mb-4">
                <div>
                    <span class="inline-block bg-sky-400/20 text-sky-400 px-3 py-1 rounded-full text-sm font-medium mb-2">
                        2021 - Presente
                    </span>
                    <h3 class="text-xl font-bold text-white">
                        Desarrollador de Software Full-Stack
                    </h3>
                    <p class="text-sky-400 font-medium">Mundos Virtuales SPA</p>
                </div>
            </div>

            <p class="text-slate-300 text-base leading-relaxed mb-4">
                Ingeniero full-stack especializado en el desarrollo completo de soluciones software-hardware. Lideré la modernización de un sistema transaccional desde una aplicación de escritorio (C# / Windows Forms) hacia una solución móvil nativa para terminales POS (PAX A8700).
            </p>

            <div class="mb-4">
                <h4 class="text-lg font-semibold text-white mb-3">Logros principales:</h4>
                <div class="experience-content">
                    <ul class="text-slate-300 space-y-2 experience-preview text-base leading-relaxed">
                        <li class="flex items-start gap-2">
                            <span class="text-sky-400 mt-1">•</span>
                            <span>Desarrollé desde cero sistema completo de conteo de billetes con interfaz Windows Forms (C#) incluyendo autenticación, dashboard, estadísticas y pantalla de conteo principal</span>
                        </li>
                        <li class="flex items-start gap-2">
                            <span class="text-sky-400 mt-1">•</span>
                            <span>Integré hardware especializado implementando comunicación serial con validadora de billetes para procesamiento automático de transacciones</span>
                        </li>
                        <li class="flex items-start gap-2">
                            <span class="text-sky-400 mt-1">•</span>
                            <span>Diseñé y desarrollé placa de control personalizada con Arduino y KiCad para centralizar control de actuadores y monitoreo de sensores en tiempo real</span>
                        </li>
                    </ul>
                    <ul class="text-slate-300 space-y-2 experience-full hidden text-base leading-relaxed">
                        <li class="flex items-start gap-2">
                            <span class="text-sky-400 mt-1">•</span>
                            <span>Desarrollé desde cero sistema completo de conteo de billetes con interfaz Windows Forms (C#) incluyendo autenticación, dashboard, estadísticas y pantalla de conteo principal</span>
                        </li>
                        <li class="flex items-start gap-2">
                            <span class="text-sky-400 mt-1">•</span>
                            <span>Integré hardware especializado implementando comunicación serial con validadora de billetes para procesamiento automático de transacciones</span>
                        </li>
                        <li class="flex items-start gap-2">
                            <span class="text-sky-400 mt-1">•</span>
                            <span>Diseñé y desarrollé placa de control personalizada con Arduino y KiCad para centralizar control de actuadores y monitoreo de sensores en tiempo real</span>
                        </li>
                        <li class="flex items-start gap-2">
                            <span class="text-sky-400 mt-1">•</span>
                            <span>Implementé sistema de control automatizado donde sensores activos disparan apertura de shutter via actuador, permitiendo flujo controlado de billetes</span>
                        </li>
                        <li class="flex items-start gap-2">
                            <span class="text-sky-400 mt-1">•</span>
                            <span>Arquitecté backend escalable con ASP.NET Core 8 y API RESTful para persistencia y gestión de datos transaccionales</span>
                        </li>
                        <li class="flex items-start gap-2">
                            <span class="text-sky-400 mt-1">•</span>
                            <span>Migré solución completa a Android nativo manteniendo toda la funcionalidad hardware-software en plataforma móvil</span>
                        </li>
                        <li class="flex items-start gap-2">
                            <span class="text-sky-400 mt-1">•</span>
                            <span>Automaticé soporte técnico regional con plataforma n8n integrando WhatsApp/Telegram para que técnicos consulten IA especializada con RAG sobre manuales Glory UWF</span>
                        </li>
                        <li class="flex items-start gap-2">
                            <span class="text-sky-400 mt-1">•</span>
                            <span>Modernicé aplicación legacy refactorizando código Python con PyQt6 para integración con contadora Glory UWF via red</span>
                        </li>
                    </ul>
                    <button class="toggle-experience mt-4 bg-sky-400/10 hover:bg-sky-400/20 text-sky-400 px-4 py-2 rounded-lg text-sm font-medium transition-all duration-200">
                        Ver más
                    </button>
                </div>
            </div>

            <div class="bg-slate-700/50 rounded-lg p-4">
                <p class="text-sm text-sky-300">
                    <strong>Stack técnico:</strong> C#, Kotlin, Python, PyQt6, Android, ASP.NET 8, PostgreSQL, Arduino, KiCad, Jetpack Compose, n8n, RAG, IA/LLM
                </p>
            </div>
        </div>
        <!-- Job 2 -->
        <div class="bg-slate-800/50 rounded-xl p-6 border-l-4 border-purple-400 hover:bg-slate-800/70 transition-all duration-300">
            <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between mb-4">
                <div>
                    <span class="inline-block bg-purple-400/20 text-purple-400 px-3 py-1 rounded-full text-sm font-medium mb-2">
                        2018 - 2021
                    </span>
                    <h3 class="text-xl font-bold text-white">
                        Embedded Systems Engineer
                    </h3>
                    <p class="text-purple-400 font-medium">Permaquim SPA</p>
                </div>
            </div>

            <div class="space-y-4 text-slate-300 text-base leading-relaxed">
                <p>
                    Optimicé y refactoricé el firmware de bajo nivel escrito en Lenguaje C para microcontroladores PIC (PIC18F4520), utilizando el entorno de desarrollo MPLAB.
                </p>
                <p>
                    El software gestiona el control y lectura de los sensores en una máquina depositaria (modelo P600), incluyendo la apertura de la bóveda y el mecanismo shutter para el paso de billetes.
                </p>
                <p>
                    Mi trabajo se enfocó en mejorar la fiabilidad, el rendimiento y la mantenibilidad del código existente, asegurando una comunicación robusta con el PC y una lectura precisa de los estados de los sensores.
                </p>
            </div>

            <div class="bg-slate-700/50 rounded-lg p-4 mt-4">
                <p class="text-sm text-purple-300">
                    <strong>Tecnologías:</strong> C, PIC18F4520, MPLAB, Sistemas Embebidos, Control de Sensores
                </p>
            </div>
        </div>

        <!-- Job 3 -->
        <div class="bg-slate-800/50 rounded-xl p-6 border-l-4 border-green-400 hover:bg-slate-800/70 transition-all duration-300">
            <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between mb-4">
                <div>
                    <span class="inline-block bg-green-400/20 text-green-400 px-3 py-1 rounded-full text-sm font-medium mb-2">
                        2014 - 2016
                    </span>
                    <h3 class="text-xl font-bold text-white">
                        Service Desk Analyst
                    </h3>
                    <p class="text-green-400 font-medium">TMH International Group</p>
                </div>
            </div>

            <p class="text-slate-300 text-base leading-relaxed mb-4">
                Administrador de sistemas y soporte técnico integral responsable de la infraestructura tecnológica empresarial. Gestioné servidores Windows Server y Linux, administré bases de datos y proporcioné soporte técnico completo a usuarios finales.
            </p>

            <div class="mb-4">
                <h4 class="text-lg font-semibold text-white mb-3">Responsabilidades principales:</h4>
                <div class="experience-content">
                    <ul class="text-slate-300 space-y-2 experience-preview text-base leading-relaxed">
                        <li class="flex items-start gap-2">
                            <span class="text-green-400 mt-1">•</span>
                            <span>Administración y mantenimiento de bases de datos del sistema empresarial</span>
                        </li>
                        <li class="flex items-start gap-2">
                            <span class="text-green-400 mt-1">•</span>
                            <span>Gestión de sitios web corporativos mediante cPanel y administración de hosting</span>
                        </li>
                        <li class="flex items-start gap-2">
                            <span class="text-green-400 mt-1">•</span>
                            <span>Instalación y configuración de equipos, software corporativo y creación de cuentas de correo</span>
                        </li>
                    </ul>
                    <ul class="text-slate-300 space-y-2 experience-full hidden text-base leading-relaxed">
                        <li class="flex items-start gap-2">
                            <span class="text-green-400 mt-1">•</span>
                            <span>Administración y mantenimiento de bases de datos del sistema empresarial</span>
                        </li>
                        <li class="flex items-start gap-2">
                            <span class="text-green-400 mt-1">•</span>
                            <span>Gestión de sitios web corporativos mediante cPanel y administración de hosting</span>
                        </li>
                        <li class="flex items-start gap-2">
                            <span class="text-green-400 mt-1">•</span>
                            <span>Instalación y configuración de equipos, software corporativo y creación de cuentas de correo</span>
                        </li>
                        <li class="flex items-start gap-2">
                            <span class="text-green-400 mt-1">•</span>
                            <span>Administración de central telefónica VoIP utilizando Digium y Elastix</span>
                        </li>
                        <li class="flex items-start gap-2">
                            <span class="text-green-400 mt-1">•</span>
                            <span>Operación y mantenimiento de servidores Windows Server (Active Directory, Exchange, Proxy)</span>
                        </li>
                        <li class="flex items-start gap-2">
                            <span class="text-green-400 mt-1">•</span>
                            <span>Administración de sistemas Linux (CentOS), FreeBSD y dispositivos NAS</span>
                        </li>
                        <li class="flex items-start gap-2">
                            <span class="text-green-400 mt-1">•</span>
                            <span>Soporte técnico integral: redes LAN, dispositivos de impresión, correo electrónico</span>
                        </li>
                        <li class="flex items-start gap-2">
                            <span class="text-green-400 mt-1">•</span>
                            <span>Gestión de accesos y permisos de software de oficina para usuarios corporativos</span>
                        </li>
                    </ul>
                    <button class="toggle-experience mt-4 bg-green-400/10 hover:bg-green-400/20 text-green-400 px-4 py-2 rounded-lg text-sm font-medium transition-all duration-200">
                        Ver más
                    </button>
                </div>
            </div>

            <div class="bg-slate-700/50 rounded-lg p-4">
                <p class="text-sm text-green-300">
                    <strong>Tecnologías:</strong> Windows Server, Linux (CentOS), FreeBSD, Active Directory, Exchange Server, VoIP (Digium/Elastix), cPanel, NAS, Redes LAN
                </p>
            </div>
        </div>
    </div>
</section>

<script>
    document.addEventListener('DOMContentLoaded', () => {
        const toggleButtons = document.querySelectorAll('.toggle-experience') as NodeListOf<HTMLButtonElement>;

        toggleButtons.forEach(button => {
            button.addEventListener('click', () => {
                const experienceContent = button.closest('.experience-content') as HTMLElement;
                const preview = experienceContent.querySelector('.experience-preview') as HTMLElement;
                const full = experienceContent.querySelector('.experience-full') as HTMLElement;

                if (full.classList.contains('hidden')) {
                    // Show full content
                    preview.classList.add('hidden');
                    full.classList.remove('hidden');
                    button.textContent = 'Ver menos';
                } else {
                    // Show preview content
                    full.classList.add('hidden');
                    preview.classList.remove('hidden');
                    button.textContent = 'Ver más';
                }
            });
        });
    });
</script>